# -*- coding: utf-8 -*-
"""Defines fixtures available to all tests."""

from .factories import (
    UserFactory,
    ProjectFactory,
    TaskFactory,
    ReminderFactory,
)
from promptyoself.app.extensions import scheduler
from promptyoself.app.database import db as _db
from promptyoself.app import create_app
import logging
import pytest
from webtest import TestApp

from ._bootstrap import ensure_app_loaded

# Ensure app is available for import
ensure_app_loaded()


@pytest.fixture(scope="session")
def app():
    """Create application for the tests."""
    if scheduler.running:
        scheduler.shutdown(wait=False)

    _app = create_app("tests.settings")

    _app.logger.setLevel(logging.CRITICAL)
    # Use app_context() directly instead of test_request_context()
    ctx = _app.app_context()
    ctx.push()

    yield _app

    ctx.pop()
    if scheduler.running:
        scheduler.shutdown(wait=False)


@pytest.fixture
def testapp(app):
    """Create Webtest app."""
    return TestApp(app)


@pytest.fixture
def client(app):
    """Flask test client."""
    return app.test_client()


@pytest.fixture
def db(app):
    """Create database for the tests."""
    # No need to push another app_context if 'app' fixture already does it for the session
    # and individual tests run within that context.
    # However, create_all and drop_all should be within a context.
    with app.app_context():
        _db.create_all()

    yield _db

    # Explicitly close DB connection
    _db.session.remove()  # Use remove() for scoped sessions
    with app.app_context():  # Ensure drop_all is also within app context
        _db.drop_all()


@pytest.fixture
def user(db):
    """Create user for the tests."""
    user = UserFactory(password="myprecious")
    db.session.commit()
    return user


@pytest.fixture
def project(db):
    """Create project for the tests."""
    project = ProjectFactory()
    db.session.commit()
    return project


@pytest.fixture
def task(db, project):
    """Create task for the tests."""
    task = TaskFactory(project=project)
    db.session.commit()
    return task


@pytest.fixture
def reminder(db, task):
    """Create reminder for the tests."""
    reminder = ReminderFactory(task=task)
    db.session.commit()
    return reminder
