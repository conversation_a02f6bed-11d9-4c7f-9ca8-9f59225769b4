# -*- coding: utf-8 -*-
"""Test forms."""

from app.forms import LoginForm, RegisterForm, TaskForm


class TestRegisterForm:
    """Register form."""

    def test_validate_user_already_registered(self, user):
        """Enter username that is already registered."""
        form = RegisterForm(
            username=user.username,
            email="<EMAIL>",
            password="example",
            confirm="example",
        )

        assert form.validate() is False
        assert "Username already registered" in form.username.errors

    def test_validate_email_already_registered(self, user):
        """Enter email that is already registered."""
        form = RegisterForm(
            username="unique", email=user.email, password="example", confirm="example"
        )

        assert form.validate() is False
        assert "Email already registered" in form.email.errors

    def test_validate_success(self, db):
        """Register with success."""
        form = RegisterForm(
            username="newusername",
            email="<EMAIL>",
            password="example",
            confirm="example",
        )
        assert form.validate() is True


class TestLoginForm:
    """Login form."""

    def test_validate_success(self, user):
        """Login successful."""
        user.save()
        form = LoginForm(username=user.username, password="myprecious")
        assert form.validate() is True
        assert form.user == user

    def test_validate_unknown_username(self, db):
        """Unknown username."""
        form = LoginForm(username="unknown", password="example")
        assert form.validate() is False
        assert "Unknown username" in form.username.errors
        assert form.user is None

    def test_validate_invalid_password(self, user):
        """Invalid password."""
        user.save()
        form = LoginForm(username=user.username, password="wrongpassword")
        assert form.validate() is False
        assert "Invalid password" in form.password.errors

    def test_validate_inactive_user(self, user):
        """Inactive user."""
        user.active = False
        user.save()
        # Correct username and password, but user is not activated
        form = LoginForm(username=user.username, password="myprecious")
        assert form.validate() is False
        assert "User not activated" in form.username.errors


class TestTaskForm:
    """Task form."""

    def test_validate_with_empty_parent_task_id(self, db):
        """Test form validation with empty parent_task_id field."""
        from promptyoself.app.models import Project
        
        # Create a test project
        project = Project.create(name="Test Project", description="Test Description")
        
        form = TaskForm(
            name="Test Task",
            description="Test Description",
            project_id=project.id,
            parent_task_id=None  # Empty parent task
        )
        
        # Manually set choices since form initialization won't work in test context
        form.project_id.choices = [(project.id, project.name)]
        form.parent_task_id.choices = [(None, 'No Parent Task')]
        
        assert form.validate() is True
        assert form.parent_task_id.data is None

    def test_validate_with_valid_parent_task_id(self, db):
        """Test form validation with valid parent_task_id."""
        from promptyoself.app.models import Project, Task
        
        # Create a test project and parent task
        project = Project.create(name="Test Project", description="Test Description")
        parent_task = Task.create(
            name="Parent Task",
            description="Parent Description",
            project_id=project.id
        )
        
        form = TaskForm(
            name="Child Task",
            description="Child Description",
            project_id=project.id,
            parent_task_id=parent_task.id
        )
        
        # Manually set choices
        form.project_id.choices = [(project.id, project.name)]
        form.parent_task_id.choices = [(None, 'No Parent Task'), (parent_task.id, parent_task.name)]
        
        assert form.validate() is True
        assert form.parent_task_id.data == parent_task.id

    def test_coerce_empty_string_to_none(self, db):
        """Test that empty string is properly coerced to None."""
        from app.models import Project
        
        # Create a test project
        project = Project.create(name="Test Project", description="Test Description")
        
        # Simulate form data with empty string for parent_task_id
        form_data = {
            'name': 'Test Task',
            'description': 'Test Description',
            'project_id': str(project.id),
            'parent_task_id': ''  # Empty string
        }
        
        form = TaskForm(data=form_data)
        
        # Manually set choices
        form.project_id.choices = [(project.id, project.name)]
        form.parent_task_id.choices = [(None, 'No Parent Task')]
        
        # The coerce function should convert empty string to None
        assert form.parent_task_id.data is None

    def test_form_validation_with_missing_required_fields(self, db):
        """Test form validation fails when required fields are missing."""
        form = TaskForm(
            name="",  # Empty name should fail validation
            description="Test Description",
            project_id="",
            parent_task_id=None
        )
        
        assert form.validate() is False
        assert "This field is required." in str(form.name.errors)
